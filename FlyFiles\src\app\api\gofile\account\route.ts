import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    const accountToken = process.env.GOFILE_ACCOUNT_TOKEN;
    const accountId = process.env.GOFILE_ACCOUNT_ID;

    if (!accountToken) {
      return NextResponse.json(
        { error: 'Account token not configured' },
        { status: 401 }
      );
    }

    // Get account details
    const response = await fetch(`https://api.gofile.io/getAccountDetails?token=${accountToken}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const result = await response.json();

    if (!response.ok) {
      return NextResponse.json(
        { error: 'Failed to get account details', details: result },
        { status: response.status }
      );
    }

    return NextResponse.json({
      accountId,
      accountToken: accountToken ? 'Present' : 'Missing',
      ...result
    });
  } catch (error) {
    console.error('Get account details error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
