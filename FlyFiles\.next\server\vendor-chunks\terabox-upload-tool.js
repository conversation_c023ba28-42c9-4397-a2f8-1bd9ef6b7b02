/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/terabox-upload-tool";
exports.ids = ["vendor-chunks/terabox-upload-tool"];
exports.modules = {

/***/ "(rsc)/./node_modules/terabox-upload-tool/lib/helpers/download/download.js":
/*!***************************************************************************!*\
  !*** ./node_modules/terabox-upload-tool/lib/helpers/download/download.js ***!
  \***************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const { generateSign, fetchHomeInfo, generateDownload } = __webpack_require__(/*! ./downloadHelper */ \"(rsc)/./node_modules/terabox-upload-tool/lib/helpers/download/downloadHelper.js\");\r\n\r\n/**\r\n * Generates a direct download link for a file from Terabox.\r\n * @param {string} ndus - User authentication token.\r\n * @param {string} fid - File ID of the file to be downloaded.\r\n * @returns {Promise<{success: boolean, message: string, downloadLink?: string}>} - A promise that resolves to an object containing success status, message, and the direct download link if successful.\r\n */\r\nasync function getDownloadLink(ndus, fid) {\r\n    try {\r\n        // Fetch home information to retrieve necessary parameters for signing\r\n        const homeInfo = await fetchHomeInfo(ndus);\r\n        \r\n        if (!homeInfo || !homeInfo.data.sign3 || !homeInfo.data.sign1 || !homeInfo.data.timestamp) {\r\n            return { success: false, message: \"Invalid home information received.\" };\r\n        }\r\n\r\n        const sign1 = homeInfo.data.sign3;\r\n        const sign2 = homeInfo.data.sign1;\r\n        const timestamp = homeInfo.data.timestamp;\r\n\r\n        // Generate the required sign using sign1 and sign2\r\n        const sign = generateSign(sign1, sign2);\r\n        if (!sign) {\r\n            return { success: false, message: \"Failed to generate sign.\" };\r\n        }\r\n\r\n        // Fetch the download link\r\n        const responseDownload = await generateDownload(sign, fid, timestamp, ndus);\r\n        if (!responseDownload || !responseDownload.downloadLink[0]?.dlink) {\r\n            return { success: false, message: \"Failed to retrieve download link.\" };\r\n        }\r\n\r\n        return { success: true, message: \"Download link retrieved successfully.\", downloadLink: responseDownload.downloadLink[0].dlink};\r\n    } catch (error) {\r\n        console.error(\"Error getting download link:\", error);\r\n        return { success: false, message: error.message || \"Unknown error occurred while fetching download link.\" };\r\n    }\r\n}\r\n\r\nmodule.exports = getDownloadLink;\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/terabox-upload-tool/lib/helpers/download/download.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/terabox-upload-tool/lib/helpers/download/downloadHelper.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/terabox-upload-tool/lib/helpers/download/downloadHelper.js ***!
  \*********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const axios = __webpack_require__(/*! axios */ \"(rsc)/./node_modules/axios/dist/node/axios.cjs\");\r\n\r\n/**\r\n * Generates a cryptographic sign using RC4-like encryption.\r\n * @param {string} s1 - First input string (key).\r\n * @param {string} s2 - Second input string (data).\r\n * @returns {string} - Base64 encoded signed string.\r\n */\r\nfunction generateSign(s1, s2) {\r\n    try {\r\n        if (!s1 || !s2) {\r\n            return { success: false, message: \"Provide both the signs\" };\r\n        }\r\n        \r\n        const p = new Uint8Array(256);\r\n        const a = new Uint8Array(256);\r\n        const result = [];\r\n\r\n        for (let i = 0; i < 256; i++) {\r\n            a[i] = s1.charCodeAt(i % s1.length);\r\n            p[i] = i;\r\n        }\r\n\r\n        let j = 0;\r\n        for (let i = 0; i < 256; i++) {\r\n            j = (j + p[i] + a[i]) % 256;\r\n            [p[i], p[j]] = [p[j], p[i]];\r\n        }\r\n\r\n        let i = 0;\r\n        j = 0;\r\n        for (let q = 0; q < s2.length; q++) {\r\n            i = (i + 1) % 256;\r\n            j = (j + p[i]) % 256;\r\n            [p[i], p[j]] = [p[j], p[i]];\r\n            const k = p[(p[i] + p[j]) % 256];\r\n            result.push(s2.charCodeAt(q) ^ k);\r\n        }\r\n\r\n        return Buffer.from(result).toString('base64');\r\n    } catch (error) {\r\n        console.error(\"Error generating sign:\", error.message);\r\n        return null;\r\n    }\r\n}\r\n\r\n/**\r\n * Fetches home information from Terabox API.\r\n * @param {string} ndus - User authentication token.\r\n * @returns {Promise<{success: boolean, message: string, data?: object}>} - The response data from the API.\r\n */\r\nasync function fetchHomeInfo(ndus) {\r\n    const url = \"https://www.1024terabox.com/api/home/<USER>";\r\n\r\n    try {\r\n        if (!ndus) {\r\n            return { success: false, message: \"User authentication token (ndus) is required\" };\r\n        }\r\n        \r\n        const response = await axios.get(url, {\r\n            params: {\r\n                app_id: \"250528\",\r\n                web: \"1\",\r\n                channel: \"dubox\",\r\n                clienttype: \"0\",\r\n            },\r\n            headers: {\r\n                \"User-Agent\": \"Mozilla/5.0\",\r\n                \"Accept\": \"application/json\",\r\n                \"Cookie\": `ndus=${ndus}`,\r\n            },\r\n        });\r\n        return { success: true, message: \"Home info retrieved successfully.\", data: response.data.data };\r\n    } catch (error) {\r\n        console.error(\"Error fetching home info:\", error.response?.data || error.message);\r\n        return { success: false, message: error.message || \"Failed to fetch home info.\" };\r\n    }\r\n}\r\n\r\n/**\r\n * Generates a download link for a file from Terabox.\r\n * @param {string} sign - Encrypted sign generated using `generateSign`.\r\n * @param {string} fid - File ID of the file to be downloaded.\r\n * @param {number} timestamp - Timestamp of the request.\r\n * @param {string} ndus - User authentication token.\r\n * @returns {Promise<{success: boolean, message: string, downloadLink?: string}>}\r\n */\r\nasync function generateDownload(sign, fid, timestamp, ndus) {\r\n    const url = \"https://www.1024terabox.com/api/download\";\r\n\r\n    try {\r\n        if (!sign || !fid || !timestamp || !ndus) {\r\n            return { success: false, message: \"Missing required parameters for generating download link.\" };\r\n        }\r\n        \r\n        const response = await axios.get(url, {\r\n            params: {\r\n                app_id: \"250528\",\r\n                web: \"1\",\r\n                channel: \"dubox\",\r\n                clienttype: \"0\",\r\n                fidlist: `[${fid}]`,\r\n                type: \"dlink\",\r\n                vip: \"2\",\r\n                sign,\r\n                timestamp,\r\n                need_speed: \"0\",\r\n            },\r\n            headers: {\r\n                \"User-Agent\": \"Mozilla/5.0\",\r\n                \"Accept\": \"application/json\",\r\n                \"Cookie\": `ndus=${ndus}`,\r\n            },\r\n        });\r\n\r\n        if (!response.data.dlink) {\r\n            return { success: false, message: \"No download link received.\" };\r\n        }\r\n\r\n        return { success: true, message: \"Download link generated successfully.\", downloadLink: response.data.dlink };\r\n    } catch (error) {\r\n        console.error(\"Error generating download link:\", error.response?.data || error.message);\r\n        return { success: false, message: error.message || \"Failed to generate download link.\" };\r\n    }\r\n}\r\n\r\nmodule.exports = {\r\n    generateSign,\r\n    fetchHomeInfo,\r\n    generateDownload,\r\n};\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/terabox-upload-tool/lib/helpers/download/downloadHelper.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/terabox-upload-tool/lib/helpers/fileDelete.js":
/*!********************************************************************!*\
  !*** ./node_modules/terabox-upload-tool/lib/helpers/fileDelete.js ***!
  \********************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const axios = __webpack_require__(/*! axios */ \"(rsc)/./node_modules/axios/dist/node/axios.cjs\");\r\n\r\n// ファイル削除APIリクエスト関数\r\nconst deleteFile = async (filelist, config) => {\r\n  // config が { credentials: { ... } } か、直接 { ndus, appId, jsToken, browserId } かを判定\r\n  const { appId, jsToken, browserId, ndus } = config.credentials || config;\r\n  const url = \"https://www.1024terabox.com/api/filemanager\"; // URLを変更\r\n\r\n  // クエリパラメータを設定\r\n  const params = {\r\n    opera: \"delete\",\r\n    app_id: appId,\r\n    jsToken: jsToken,\r\n  };\r\n\r\n  // URLSearchParamsは自動的にエンコードしますが、\r\n  // さらに明示的にエンコードしたい場合は encodeURIComponent を使います。\r\n  const data = new URLSearchParams();\r\n  // JSON形式の filelist をエンコードして追加\r\n  data.append(\"filelist\", JSON.stringify(filelist));\r\n\r\n  // ヘッダー情報を設定\r\n  const headers = {\r\n    \"Cookie\": `browserid=${browserId}; ndus=${ndus};`,\r\n  };\r\n\r\n  try {\r\n    // APIリクエストを送信\r\n    const response = await axios.post(url, data.toString(), {\r\n      headers,\r\n      params,\r\n    });\r\n\r\n    // レスポンスを返す\r\n    return response.data;\r\n  } catch (error) {\r\n    // エラーを投げる\r\n    throw error.response ? error.response.data : error.message;\r\n  }\r\n};\r\n\r\n// モジュールとしてエクスポート\r\nmodule.exports = {\r\n  deleteFile,\r\n};\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/terabox-upload-tool/lib/helpers/fileDelete.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/terabox-upload-tool/lib/helpers/fileMove.js":
/*!******************************************************************!*\
  !*** ./node_modules/terabox-upload-tool/lib/helpers/fileMove.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const axios = __webpack_require__(/*! axios */ \"(rsc)/./node_modules/axios/dist/node/axios.cjs\");\r\n\r\n// ファイル移動APIリクエスト関数\r\nconst moveFile = async (filelist, config) => {\r\n  // config が { credentials: { ... } } か、直接 { ndus, appId, jsToken, browserId } かを判定\r\n  const { appId, jsToken, browserId, ndus } = config.credentials || config;\r\n  const url = \"https://www.1024terabox.com/api/filemanager\"; // URLを変更\r\n\r\n  // クエリパラメータを設定（operaの値を \"move\" に変更）\r\n  const params = {\r\n    opera: \"move\",\r\n    app_id: appId,\r\n    jsToken: jsToken,\r\n  };\r\n\r\n  // URLSearchParamsを使用してForm dataを作成\r\n  const data = new URLSearchParams();\r\n  // JSON形式の filelist をエンコードして追加\r\n  // 例: [{\"path\":\"/b\",\"dest\":\"/a\",\"newname\":\"c\"}]\r\n  data.append(\"filelist\", JSON.stringify(filelist));\r\n\r\n  // ヘッダー情報を設定\r\n  const headers = {\r\n    \"Cookie\": `browserid=${browserId}; ndus=${ndus};`,\r\n    \"Content-Type\": \"application/x-www-form-urlencoded\",\r\n  };\r\n\r\n  try {\r\n    // APIリクエストを送信\r\n    const response = await axios.post(url, data.toString(), {\r\n      headers,\r\n      params,\r\n    });\r\n\r\n    // レスポンスを返す\r\n    return response.data;\r\n  } catch (error) {\r\n    // エラーを投げる\r\n    throw error.response ? error.response.data : error.message;\r\n  }\r\n};\r\n\r\n// モジュールとしてエクスポート\r\nmodule.exports = {\r\n  moveFile,\r\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/terabox-upload-tool/lib/helpers/fileMove.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/terabox-upload-tool/lib/helpers/getShortUrl.js":
/*!*********************************************************************!*\
  !*** ./node_modules/terabox-upload-tool/lib/helpers/getShortUrl.js ***!
  \*********************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const axios = __webpack_require__(/*! axios */ \"(rsc)/./node_modules/axios/dist/node/axios.cjs\");\r\n\r\n/**\r\n * Generates a short URL for a file in Terabox.\r\n * @param {string} ndus - User authentication token.\r\n * @param {string} path - The file path in Terabox.\r\n * @param {string} fid - The file ID.\r\n * @returns {Promise<object|null>} - A promise that resolves to the API response containing the short URL or null if an error occurs.\r\n */\r\nconst getShortUrl = async (ndus, path, fid) => {\r\n    try {\r\n        // API Endpoint\r\n        const url = 'https://www.1024terabox.com/share/pset';\r\n        const cookies = `ndus=${ndus}`;\r\n\r\n        // Form Data Parameters\r\n        const formData = new URLSearchParams({\r\n            app_id: '250528',\r\n            web: '1',\r\n            channel: 'dubox',\r\n            clienttype: '0',\r\n            app: 'universe',\r\n            schannel: '0',\r\n            channel_list: '[0]',\r\n            period: '0',\r\n            path_list: `[\"${path}\"]`,\r\n            fid_list: `[${fid}]`,\r\n            pwd: '',\r\n            public: '1',\r\n            scene: ''\r\n        });\r\n\r\n        // Request Headers\r\n        const headers = {\r\n            'Content-Type': 'application/x-www-form-urlencoded',\r\n            'Cookie': cookies,\r\n            'Referer': 'https://www.1024terabox.com/',\r\n            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'\r\n        };\r\n\r\n        // Send POST request to generate a short URL\r\n        const response = await axios.post(url, formData.toString(), { headers });\r\n\r\n        // Return the API response containing the short URL\r\n        return response.data;\r\n    } catch (error) {\r\n        console.error('Error generating short link:', error);\r\n        return null;\r\n    }\r\n};\r\n\r\nmodule.exports = getShortUrl;\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/terabox-upload-tool/lib/helpers/getShortUrl.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/terabox-upload-tool/lib/helpers/utils.js":
/*!***************************************************************!*\
  !*** ./node_modules/terabox-upload-tool/lib/helpers/utils.js ***!
  \***************************************************************/
/***/ ((module) => {

eval("/**\r\n * Builds the URL for uploading a file chunk.\r\n * @param {string} fileName - The name of the file being uploaded\r\n * @param {string} uploadId - The unique ID for the upload session (optional)\r\n * @param {string} appId - The application ID (required)\r\n * @returns {string} - The upload URL\r\n */\r\nfunction buildUploadUrl(fileName, uploadId, appId) {\r\n  return `https://c-jp.1024terabox.com/rest/2.0/pcs/superfile2?method=upload&app_id=${appId}&channel=dubox&clienttype=0&web=1&path=%2F${encodeURIComponent(fileName)}&uploadid=${uploadId}&uploadsign=0&partseq=0`;\r\n}\r\n\r\n/**\r\n * Builds the URL for the final create call.\r\n * @returns {string} - The create URL\r\n */\r\nfunction buildCreateUrl() {\r\n  return 'https://www.1024terabox.com/api/create';\r\n}\r\n\r\n/**\r\n * Builds the URL for fetching the file list.\r\n * @param {string} appId - The application ID\r\n * @param {string} directory - The directory path to fetch the file list from\r\n * @returns {string} - The file list URL\r\n */\r\nfunction buildListUrl(appId, directory) {\r\n  return `https://www.1024terabox.com/api/list?app_id=${appId}&web=1&channel=dubox&clienttype=0&order=time&desc=1&dir=${encodeURIComponent(directory)}&num=100&page=1&showempty=0`;\r\n}\r\n\r\n\r\n/**\r\n * Builds the downloadable link for videos.\r\n * @param {string} appId - The application ID\r\n * @param {string} videoPath - The directory path to fetch the file list from\r\n * @returns {string} - The file list URL\r\n */\r\nfunction buildVideoDownloadUrl(appId, videoPath) {\r\n  return `https://www.1024terabox.com/api/streaming?path=${encodeURIComponent(videoPath)}&app_id=${appId}&clienttype=0&type=M3U8_FLV_264_480&vip=1`;\r\n}\r\n\r\nmodule.exports = { buildUploadUrl, buildCreateUrl, buildListUrl, buildVideoDownloadUrl };\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/terabox-upload-tool/lib/helpers/utils.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/terabox-upload-tool/lib/index.js":
/*!*******************************************************!*\
  !*** ./node_modules/terabox-upload-tool/lib/index.js ***!
  \*******************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const axios = __webpack_require__(/*! axios */ \"(rsc)/./node_modules/axios/dist/node/axios.cjs\");\r\nconst path = __webpack_require__(/*! path */ \"path\");\r\nconst fs = __webpack_require__(/*! fs */ \"fs\");\r\nconst FormData = __webpack_require__(/*! form-data */ \"(rsc)/./node_modules/form-data/lib/form_data.js\");\r\nconst { buildUploadUrl, buildCreateUrl, buildListUrl } = __webpack_require__(/*! ./helpers/utils */ \"(rsc)/./node_modules/terabox-upload-tool/lib/helpers/utils.js\");\r\nconst getDownloadLink = __webpack_require__(/*! ./helpers/download/download */ \"(rsc)/./node_modules/terabox-upload-tool/lib/helpers/download/download.js\");\r\nconst { deleteFile } = __webpack_require__(/*! ./helpers/fileDelete */ \"(rsc)/./node_modules/terabox-upload-tool/lib/helpers/fileDelete.js\");\r\nconst { moveFile } = __webpack_require__(/*! ./helpers/fileMove */ \"(rsc)/./node_modules/terabox-upload-tool/lib/helpers/fileMove.js\");\r\nconst getShortUrl = __webpack_require__(/*! ./helpers/getShortUrl */ \"(rsc)/./node_modules/terabox-upload-tool/lib/helpers/getShortUrl.js\");\r\n\r\n/**\r\n * Class to handle Terabox file operations\r\n */\r\nclass TeraboxUploader {\r\n  constructor(credentials) {\r\n    // Now require jsToken and browserId for deletion functionality\r\n    if (\r\n      !credentials ||\r\n      !credentials.ndus ||\r\n      !credentials.appId ||\r\n      !credentials.uploadId ||\r\n      !credentials.jsToken ||\r\n      !credentials.browserId\r\n    ) {\r\n      throw new Error(\"Credentials are required (ndus, appId, uploadId, jsToken, browserId).\");\r\n    }\r\n\r\n    this.credentials = {\r\n      ndus: credentials.ndus,\r\n      cookies: `browserid=3BeB9xGWg2yuzOuPRnKtO0ZQx990OtItXpdwkRVAIKYiLxBkT8yVYM3TnVr=; lang=en; ndus=${credentials.ndus};`,\r\n      appId: credentials.appId,\r\n      uploadId: credentials.uploadId,\r\n      jsToken: credentials.jsToken,\r\n      browserId: credentials.browserId,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Uploads a file to Terabox\r\n   * @param {string} filePath - Path to the file to be uploaded\r\n   * @param {function} progressCallback - Optional callback to track upload progress\r\n   * @param {string} [directory='/'] - Optional directory where the file will be saved on Terabox\r\n   * @returns {Promise<{success: boolean, message: string, fileDetails?: object}>} - A promise that resolves to an object indicating the result of the upload:\r\n   *   - `success` (boolean): `true` if the upload was successful, `false` otherwise.\r\n   *   - `message` (string): A message with the upload status (success or error).\r\n   *   - `fileDetails` (optional object): The details of the file uploaded, returned only if the upload was successful.\r\n   */\r\n  async uploadFile(filePath, progressCallback, directory = '/') {\r\n    try {\r\n      const fileName = path.basename(filePath);\r\n      const fileSize = fs.statSync(filePath).size;\r\n      const uploadUrl = buildUploadUrl(fileName, this.credentials.uploadId, this.credentials.appId);\r\n\r\n      // Preflight request (OPTIONS)\r\n      await axios.options(uploadUrl, { headers: { Origin: 'https://www.1024terabox.com' } });\r\n\r\n      // Upload the file as form data (POST)\r\n      const formData = new FormData();\r\n      formData.append('file', fs.createReadStream(filePath));\r\n\r\n      const postResponse = await axios.post(uploadUrl, formData, {\r\n        headers: {\r\n          ...formData.getHeaders(),\r\n          Origin: 'https://www.1024terabox.com',\r\n          Cookie: this.credentials.cookies,\r\n        },\r\n        onUploadProgress: (progressEvent) => {\r\n          if (progressCallback) {\r\n            progressCallback(progressEvent.loaded, progressEvent.total);\r\n          }\r\n        },\r\n      });\r\n\r\n      // Finalize the upload (Create call)\r\n      const createUrl = buildCreateUrl();\r\n      const createResponse = await axios.post(\r\n        createUrl,\r\n        new URLSearchParams({\r\n          path: `${directory}/${fileName}`,\r\n          size: fileSize,\r\n          uploadid: this.credentials.uploadId,\r\n          target_path: directory,\r\n          block_list: JSON.stringify([postResponse.headers['content-md5']]),\r\n          local_mtime: Math.floor(Date.now() / 1000),\r\n        }).toString(),\r\n        {\r\n          headers: {\r\n            'Content-Type': 'application/x-www-form-urlencoded',\r\n            Cookie: this.credentials.cookies,\r\n          },\r\n        }\r\n      );\r\n\r\n      // Return success message\r\n      return {\r\n        success: true,\r\n        message: 'File uploaded successfully.',\r\n        fileDetails: createResponse.data,\r\n      };\r\n    } catch (error) {\r\n\r\n      // Return failure message\r\n      return {\r\n        success: false,\r\n        message: error.response?.data || error.message,\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Fetches the file list from Terabox\r\n   * @param {string} directory - Directory to fetch the file list from (e.g., \"/\")\r\n   * @returns {Promise<object>} - JSON response with file details along with download link\r\n   */\r\n  async fetchFileList(directory = '/') {\r\n    try {\r\n      const listUrl = buildListUrl(this.credentials.appId, directory);\r\n\r\n      const response = await axios.get(listUrl, {\r\n        headers: {\r\n          Cookie: this.credentials.cookies,\r\n        },\r\n      });\r\n\r\n      return { success: true, message: 'File list retrieved successfully.', data: response.data };\r\n    } catch (error) {\r\n      console.error('Error fetching file list:', error);\r\n      return {\r\n        success: false,\r\n        message: error.response?.data?.error || error.message || 'Failed to fetch file list.',\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n * Uploads a file to Terabox\r\n * @param {int} fileId - fs_id in file details\r\n * @returns {Promise<{success: boolean, message: string, fileDetails?: object}>} - A promise that resolves to an object with download link.\r\n */\r\n  async downloadFile(fileId) {\r\n    try {\r\n      const ndus = this.credentials.ndus;\r\n      const fid = fileId;\r\n      const response = await getDownloadLink(ndus, fid);\r\n      return response\r\n    } catch (error) {\r\n      return {\r\n        success: false,\r\n        message: error.response?.data?.error || error.message || 'Failed to fetch file list.',\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Deletes files from Terabox using the deletion API.\r\n   * @param {Array} fileList - List of file paths to be deleted on Terabox\r\n   * @returns {Promise<any>} - A promise that resolves to the deletion result.\r\n   */\r\n  async deleteFiles(fileList) {\r\n    try {\r\n      const config = {\r\n        ndus: this.credentials.ndus,\r\n        appId: this.credentials.appId,\r\n        jsToken: this.credentials.jsToken,\r\n        browserId: this.credentials.browserId,\r\n      };\r\n      const result = await deleteFile(fileList, config);\r\n      return { success: true, message: 'Files deleted successfully.', result };\r\n    } catch (error) {\r\n      return {\r\n        success: false,\r\n        message: error.response?.data?.error || error.message || 'Failed to delete files.',\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Moves files on Terabox using the move API.\r\n   * Moves a file from one location to another with a new name.\r\n   * @param {string} sourcePath - The current path of the file.\r\n   * @param {string} destinationPath - The destination directory where the file will be moved.\r\n   * @param {string} newName - The new name for the file.\r\n   * @returns {Promise<any>} - A promise that resolves to the move result.\r\n   */\r\n  async moveFiles(sourcePath, destinationPath, newName) {\r\n    try {\r\n      const config = {\r\n        ndus: this.credentials.ndus,\r\n        appId: this.credentials.appId,\r\n        jsToken: this.credentials.jsToken,\r\n        browserId: this.credentials.browserId,\r\n      };\r\n      // Create file list for move operation as per API: [{\"path\":\"/b\",\"dest\":\"/a\",\"newname\":\"c\"}]\r\n      const fileList = [{\r\n        path: sourcePath,\r\n        dest: destinationPath,\r\n        newname: newName\r\n      }];\r\n      const result = await moveFile(fileList, config);\r\n      return result;\r\n    } catch (error) {\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Generates a short URL for a file on Terabox\r\n   * @param {string} filePath - The file path on Terabox\r\n   * @param {string} fileId - The file ID on Terabox\r\n   * @returns {Promise<object>} - The API response containing the short URL\r\n   */\r\n  async generateShortUrl(filePath, fileId) {\r\n    try {\r\n      const shortUrlResponse = await getShortUrl(this.credentials.ndus, filePath, fileId);\r\n      if (shortUrlResponse && shortUrlResponse.errno === 0) {\r\n        return {\r\n          success: true,\r\n          message: 'Short URL generated successfully.',\r\n          shortUrl: shortUrlResponse.shorturl,\r\n        };\r\n      } else {\r\n        return {\r\n          success: false,\r\n          message: shortUrlResponse?.errmsg || 'Failed to generate short URL.',\r\n        };\r\n      }\r\n    } catch (error) {\r\n      return {\r\n        success: false,\r\n        message: error.message || 'An error occurred while generating the short URL.',\r\n      };\r\n    }\r\n  }\r\n}\r\n\r\nmodule.exports = TeraboxUploader;\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/terabox-upload-tool/lib/index.js\n");

/***/ })

};
;