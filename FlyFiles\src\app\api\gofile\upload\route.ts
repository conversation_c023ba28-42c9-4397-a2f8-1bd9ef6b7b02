import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      );
    }

    // Create FormData for GoFile.io API
    const goFileFormData = new FormData();
    goFileFormData.append('file', file);

    // Add authentication token if available
    const accountToken = process.env.GOFILE_ACCOUNT_TOKEN;
    if (accountToken) {
      goFileFormData.append('token', accountToken);
    }

    // Upload to GoFile.io
    const uploadUrl = process.env.GOFILE_UPLOAD_URL || 'https://upload.gofile.io/uploadfile';
    const response = await fetch(uploadUrl, {
      method: 'POST',
      body: goFileFormData,
    });

    const result = await response.json();

    if (!response.ok) {
      return NextResponse.json(
        { error: 'GoFile.io upload failed', details: result },
        { status: response.status }
      );
    }

    return NextResponse.json(result);
  } catch (error) {
    console.error('Upload error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
